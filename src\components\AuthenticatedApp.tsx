
import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from '@/components/ui/toaster';
import Navigation from '@/components/Navigation';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import Dashboard from '@/components/Dashboard';
import SchoolManagement from '@/components/SchoolManagement';
import SchoolList from '@/components/schools/SchoolList';
import PrimarySchools from '@/components/schools/PrimarySchools';
import SecondarySchools from '@/components/schools/SecondarySchools';
import InactiveSchools from '@/components/schools/InactiveSchools';
import SchoolLocations from '@/components/schools/SchoolLocations';
import Books from '@/components/Books';
import UnifiedTaskManagement from '@/components/tasks/UnifiedTaskManagement';
import StaffManagement from '@/components/staff/StaffManagement';

import ActiveDistributions from '@/components/distributions/ActiveDistributions';
import ConsolidatedDistributionManagement from '@/components/distributions/ConsolidatedDistributionManagement';

import DistributionHistory from '@/components/distributions/DistributionHistory';
import Reports from '@/components/Reports';
import Analytics from '@/components/reports/Analytics';
import Performance from '@/components/reports/Performance';
import CustomReports from '@/components/reports/CustomReports';
import PerformanceTest from '@/components/PerformanceTest';
import ComprehensiveReports from '@/components/reports/ComprehensiveReports';
import ImpactOverview from '@/components/impact/ImpactOverview';
import ComprehensiveImpactDashboard from '@/components/impact/analytics/ComprehensiveImpactDashboard';
import StudentOutcomesModule from '@/components/impact/student-outcomes/StudentOutcomesModule';
import SchoolPerformanceDashboard from '@/components/impact/school-performance/SchoolPerformanceDashboard';
import StudentLeadershipTrainingModule from '@/components/impact/student-leadership/StudentLeadershipTrainingModule';
import BeneficiaryFeedbackModule from '@/components/impact/beneficiary-feedback/BeneficiaryFeedbackModule';
import LongTermImpactModule from '@/components/impact/longitudinal-tracking/LongTermImpactModule';
import ImpactReports from '@/components/impact/analytics/ImpactReports';
import AdminOnlyWrapper from '@/components/impact/AdminOnlyWrapper';
import SessionManagement from '@/components/attendance/SessionManagement';
import GPSCheckIn from '@/components/attendance/GPSCheckIn';
import AttendanceReportsHub from '@/components/attendance/reports/AttendanceReportsHub';
import AttendanceNotificationCenter from '@/components/attendance/notifications/AttendanceNotificationCenter';
import LocationLogs from '@/components/attendance/LocationLogs';
import AttendanceAnalyticsDashboard from '@/components/attendance/AttendanceAnalyticsDashboard';
import ConsolidatedStaffReports from '@/components/attendance/ConsolidatedStaffReports';
import UnifiedAttendanceAnalytics from '@/components/attendance/UnifiedAttendanceAnalytics';
import UnifiedFieldVisits from '@/components/attendance/UnifiedFieldVisits';
import { FieldStaffCheckIn, FieldStaffCheckOut, FieldStaffAttendance, AdminTimesheetDashboard, FieldReportingAnalytics } from '@/components/field-staff';
import { HelpSupport, Documentation } from '@/components/help';
import { useAuth } from '@/hooks/useAuth';

const queryClient = new QueryClient();

const AuthenticatedApp = () => {
  const { profile } = useAuth();
  const [currentView, setCurrentView] = useState('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const renderMainContent = () => {
    switch (currentView) {
      case 'dashboard':
        return <Dashboard onViewChange={setCurrentView} />;
      
      // Schools
      case 'schools':
      case 'schools-list':
        return <SchoolList />;
      case 'schools-primary':
        return <PrimarySchools />;
      case 'schools-secondary':
        return <SecondarySchools />;
      case 'schools-inactive':
        return <InactiveSchools />;
      case 'schools-locations':
        return <SchoolLocations />;

      // Books - Admin only - Consolidated routing with tabs
      case 'books':
      case 'book-management':
      case 'book-distributions':
        return (
          <AdminOnlyWrapper>
            <Books />
          </AdminOnlyWrapper>
        );


      // Tasks - Unified routing
      case 'tasks':
      case 'tasks-list':
      case 'tasks-assigned':
      case 'tasks-managed':
      case 'tasks-completed':
      case 'tasks-overdue':
        return <UnifiedTaskManagement />;

      // Field Visits - Unified for all roles
      case 'field-visits':
        return <UnifiedFieldVisits />;

      // Legacy attendance route - redirect to field-visits
      case 'attendance':
        return <UnifiedFieldVisits />;
      case 'field-staff-checkin':
        return <FieldStaffCheckIn />;
      case 'field-staff-checkout':
        return <FieldStaffCheckOut />;
      case 'field-staff-timesheets':
        return <AdminTimesheetDashboard />;
      case 'field-staff-reports':
        return <AdminTimesheetDashboard />; // Can be extended later for dedicated reports view
      case 'field-staff-analytics':
        return <FieldReportingAnalytics />;

      // Legacy session management routes - redirect to unified field visits
      case 'attendance-sessions':
      case 'session-management':
      case 'attendance-gps':
        return <UnifiedFieldVisits />;
      case 'attendance-reports':
        return <AttendanceReportsHub />;
      case 'attendance-notifications':
        return <AttendanceNotificationCenter />;
      case 'attendance-location-logs':
        return <LocationLogs />;


      // Distributions
      case 'distributions':
      case 'distributions-active':
        return <ActiveDistributions />;
      case 'distributions-history':
        return <DistributionHistory />;
      
      // Reports - Now consolidated under Impact
      case 'reports':
        return (
          <AdminOnlyWrapper>
            <ComprehensiveReports />
          </AdminOnlyWrapper>
        );

      // Impact Measurement - Consolidated (Admin Only)
      case 'impact':
      case 'impact-overview':
        return (
          <AdminOnlyWrapper>
            <ImpactOverview defaultTab="overview" />
          </AdminOnlyWrapper>
        );

      case 'impact-student-outcomes':
        return (
          <AdminOnlyWrapper>
            <ImpactOverview defaultTab="student" />
          </AdminOnlyWrapper>
        );



      case 'impact-beneficiary-feedback':
        return (
          <AdminOnlyWrapper>
            <ImpactOverview defaultTab="beneficiary-feedback" />
          </AdminOnlyWrapper>
        );



      // Help & Support
      case 'help':
        return <HelpSupport onViewChange={setCurrentView} />;
      case 'help-docs':
        return <Documentation />;

      // Staff Management - Admin only
      case 'staff-management':
        return (
          <AdminOnlyWrapper>
            <StaffManagement />
          </AdminOnlyWrapper>
        );

      // Settings - Main route links to preferences component
      case 'settings':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold mb-4">Settings & Preferences</h1>
            <p className="text-gray-600">User preferences and application settings will be available here.</p>
          </div>
        );

      // Settings subcategories (placeholder)
      case 'settings-profile':
      case 'settings-notifications':
        return (
          <div className="p-6">
            <h1 className="text-2xl font-bold mb-4">Coming Soon</h1>
            <p className="text-gray-600">This feature is under development.</p>
          </div>
        );
      
      default:
        return <Dashboard />;
    }
  };

  if (!profile) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  return (
    <QueryClientProvider client={queryClient}>
      <div className="min-h-screen bg-gray-50 flex flex-col">
        {/* Header */}
        <Header
          onMenuToggle={() => setSidebarOpen(!sidebarOpen)}
          onViewChange={setCurrentView}
        />

        {/* Main Content Area - Add top padding for fixed header */}
        <div className="flex flex-1 overflow-hidden relative" style={{ paddingTop: 'var(--header-height)' }}>
          {/* Mobile Sidebar Overlay */}
          {sidebarOpen && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
              onClick={() => setSidebarOpen(false)}
            />
          )}

          {/* Sidebar */}
          <div
            className={`${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} lg:translate-x-0 fixed inset-y-0 left-0 z-40 w-64 bg-white border-r border-gray-200 shadow-sm transition-transform duration-300 ease-in-out`}
            style={{ paddingTop: 'var(--header-height)' }}
          >
            <Navigation
              currentUser={profile}
              currentView={currentView}
              onViewChange={(view) => {
                setCurrentView(view);
                setSidebarOpen(false); // Close sidebar on mobile after navigation
              }}
            />
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col overflow-hidden lg:ml-64">
            <main className="flex-1 overflow-y-auto">
              {renderMainContent()}
            </main>

            {/* Footer */}
            <Footer onViewChange={setCurrentView} />
          </div>
        </div>
      </div>
      <Toaster />
    </QueryClientProvider>
  );
};

export default AuthenticatedApp;
