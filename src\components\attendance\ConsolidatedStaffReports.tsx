import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Clock, 
  FileText, 
  Bell, 
  Users, 
  Calendar,
  Search,
  Filter,
  Download,
  TrendingUp,
  BarChart3,
  AlertTriangle
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { 
  useFieldStaffTimesheets, 
  useFieldReports, 
  useFieldStaffAttendanceStatus 
} from '@/hooks/field-staff/useFieldStaffAttendance';
import { 
  useAttendanceNotifications, 
  useNotificationStats 
} from '@/hooks/attendance/useAttendanceNotifications';
import { <PERSON>Layout, PageHeader, ContentCard } from '@/components/layout';
import AdminTimesheetDashboard from '../field-staff/AdminTimesheetDashboard';
import FieldReportingAnalytics from '../field-staff/FieldReportingAnalytics';
import AttendanceNotificationCenter from './notifications/AttendanceNotificationCenter';

const ConsolidatedStaffReports = () => {
  const { profile } = useAuth();
  const [activeTab, setActiveTab] = useState('timesheets');
  const [dateFilter, setDateFilter] = useState(new Date().toISOString().split('T')[0]);
  const [staffFilter, setStaffFilter] = useState('all');

  // Data fetching
  const { data: timesheets } = useFieldStaffTimesheets(dateFilter, staffFilter === 'all' ? undefined : staffFilter);
  const { data: fieldReports } = useFieldReports(staffFilter === 'all' ? undefined : staffFilter, dateFilter, dateFilter);
  const { data: notifications } = useAttendanceNotifications(undefined, undefined, false);
  const { data: notificationStats } = useNotificationStats();
  const { data: attendanceStatus } = useFieldStaffAttendanceStatus(dateFilter, staffFilter === 'all' ? undefined : staffFilter);

  // Role-based permissions
  const canViewAllReports = profile?.role === 'admin' || profile?.role === 'program_officer';

  // Calculate summary stats
  const getSummaryStats = () => {
    const totalStaff = attendanceStatus?.length || 0;
    const activeStaff = attendanceStatus?.filter(s => s.current_status === 'checked_in').length || 0;
    const totalHours = timesheets?.reduce((sum, t) => sum + (t.total_work_hours || 0), 0) || 0;
    const totalReports = fieldReports?.length || 0;
    const unreadNotifications = notifications?.filter(n => !n.read_at).length || 0;

    return {
      totalStaff,
      activeStaff,
      totalHours,
      totalReports,
      unreadNotifications
    };
  };

  const stats = getSummaryStats();

  // Tab configuration
  const availableTabs = [
    {
      id: 'timesheets',
      label: 'Staff Timesheets',
      icon: Clock,
      count: timesheets?.length || 0,
      description: 'Daily work hours and productivity tracking'
    },
    {
      id: 'field-reports',
      label: 'Field Reports',
      icon: FileText,
      count: fieldReports?.length || 0,
      description: 'Field activity reports and analytics'
    },
    {
      id: 'notifications',
      label: 'Notifications',
      icon: Bell,
      count: stats.unreadNotifications,
      description: 'Attendance alerts and communications'
    }
  ];

  return (
    <PageLayout>
      <PageHeader
        title="Staff Reports"
        description="Comprehensive staff reporting dashboard for timesheets, field reports, and notifications"
        icon={Users}
      >
        <div className="flex items-center gap-2">
          <Badge variant={stats.activeStaff > 0 ? "default" : "secondary"}>
            {stats.activeStaff} Active Staff
          </Badge>
          {stats.unreadNotifications > 0 && (
            <Badge variant="destructive">
              {stats.unreadNotifications} Unread
            </Badge>
          )}
        </div>
      </PageHeader>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Users className="h-4 w-4" />
              Staff Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeStaff}/{stats.totalStaff}</div>
            <p className="text-xs text-muted-foreground">
              Currently active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Total Hours
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalHours.toFixed(1)}</div>
            <p className="text-xs text-muted-foreground">
              Hours worked today
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Field Reports
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalReports}</div>
            <p className="text-xs text-muted-foreground">
              Reports submitted
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Bell className="h-4 w-4" />
              Notifications
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.unreadNotifications}</div>
            <p className="text-xs text-muted-foreground">
              Unread alerts
            </p>
          </CardContent>
        </Card>
      </div>

      <ContentCard>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          {/* Tab Navigation */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
            <TabsList className="grid w-full grid-cols-3 sm:w-auto">
              {availableTabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <TabsTrigger key={tab.id} value={tab.id} className="flex items-center gap-2">
                    <Icon className="h-4 w-4" />
                    <span className="hidden sm:inline">{tab.label}</span>
                    {tab.count > 0 && (
                      <Badge variant="secondary" className="ml-1">
                        {tab.count}
                      </Badge>
                    )}
                  </TabsTrigger>
                );
              })}
            </TabsList>

            {/* Common Filters */}
            <div className="flex items-center gap-2">
              <Input
                type="date"
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                className="w-40"
              />
              
              {canViewAllReports && (
                <Select value={staffFilter} onValueChange={setStaffFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="All Staff" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Staff</SelectItem>
                    {/* Add staff options dynamically */}
                  </SelectContent>
                </Select>
              )}

              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-1" />
                Export
              </Button>
            </div>
          </div>

          {/* Tab Content */}
          <TabsContent value="timesheets" className="mt-0">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">Staff Timesheets</h3>
                  <p className="text-sm text-muted-foreground">
                    Monitor daily activities and performance of field staff members
                  </p>
                </div>
              </div>
              
              {/* Embed the existing AdminTimesheetDashboard component */}
              <div className="border rounded-lg">
                <AdminTimesheetDashboard />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="field-reports" className="mt-0">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">Field Reports & Analytics</h3>
                  <p className="text-sm text-muted-foreground">
                    Track field staff productivity, school visit patterns, and impact measurement data
                  </p>
                </div>
              </div>
              
              {/* Embed the existing FieldReportingAnalytics component */}
              <div className="border rounded-lg">
                <FieldReportingAnalytics />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="notifications" className="mt-0">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">Attendance Notifications</h3>
                  <p className="text-sm text-muted-foreground">
                    Manage attendance alerts, reminders, and staff communications
                  </p>
                </div>
              </div>
              
              {/* Show notification summary */}
              {stats.unreadNotifications > 0 && (
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-center text-yellow-800">
                    <AlertTriangle className="h-5 w-5 mr-2" />
                    <span className="font-medium">
                      {stats.unreadNotifications} unread notification(s) require attention
                    </span>
                  </div>
                </div>
              )}
              
              {/* Embed the existing AttendanceNotificationCenter component */}
              <div className="border rounded-lg">
                <AttendanceNotificationCenter />
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </ContentCard>
    </PageLayout>
  );
};

export default ConsolidatedStaffReports;
